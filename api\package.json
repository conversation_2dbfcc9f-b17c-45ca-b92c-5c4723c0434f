{"name": "ctrack-locator-api", "version": "1.0.0", "description": "TypeScript API server for C-Track Locator", "main": "index.js", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "axios": "^1.6.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "typescript": "^5.3.0", "ts-node-dev": "^2.0.0"}}