import { campusLocations } from '../config';
import { GPSMatch, CampusLocation } from '../types';

/**
 * Calculate Haversine distance between two points in meters
 */
export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const phi1 = (lat1 * Math.PI) / 180;
  const phi2 = (lat2 * Math.PI) / 180;
  const dphi = ((lat2 - lat1) * Math.PI) / 180;
  const dlambda = ((lon2 - lon1) * Math.PI) / 180;

  const a = Math.sin(dphi / 2) ** 2 +
    Math.cos(phi1) * Math.cos(phi2) * Math.sin(dlambda / 2) ** 2;
  
  return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
}

/**
 * Find the nearest campus location to given GPS coordinates
 */
export function findNearestLocation(userLat: number, userLng: number): GPSMatch {
  let nearestLocation: CampusLocation | null = null;
  let minDistance = Infinity;

  for (const loc of campusLocations) {
    const dist = calculateDistance(userLat, userLng, loc.lat, loc.lng);
    if (dist < minDistance) {
      minDistance = dist;
      nearestLocation = loc;
    }
  }

  const maxDistance = 200; // meters
  const confidence = Math.max(0, Math.min(1, 1 - (minDistance / maxDistance)));

  return {
    name: nearestLocation?.name || null,
    distance: minDistance,
    confidence,
    coordinates: nearestLocation || null
  };
}
