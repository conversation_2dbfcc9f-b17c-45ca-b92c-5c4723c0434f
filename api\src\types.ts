export interface CampusLocation {
  name: string;
  lat: number;
  lng: number;
}

export interface GPSMatch {
  name: string | null;
  distance: number;
  confidence: number;
  coordinates: CampusLocation | null;
}

export interface AIPrediction {
  predicted_class: string;
  probabilities: Record<string, number>;
}

export interface HybridPrediction {
  final_location: string;
  final_confidence: number;
  gps_contribution: number;
  ai_contribution: number;
  method: 'hybrid' | 'gps-only' | 'ai-only';
  location_scores: Record<string, number>;
}

export interface PredictionResponse {
  predicted_class: string;
  confidence: number;
  probabilities: Record<string, number>;
  hybrid_prediction?: HybridPrediction;
  gps_data?: GPSMatch;
  ai_data?: AIPrediction;
  method: string;
}

export interface RoboflowPrediction {
  class: string;
  confidence: number;
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface RoboflowResponse {
  predictions: RoboflowPrediction[];
  image: {
    width: number;
    height: number;
  };
}
