# C-Track Locator TypeScript API

A TypeScript/Node.js API server that replicates the functionality of the Python Flask server, providing location prediction using Roboflow AI and GPS data.

## Features

- **AI-powered location prediction** using Roboflow computer vision API
- **GPS-based location matching** with campus coordinates
- **Hybrid prediction system** combining AI (60%) and GPS (40%) with configurable weights
- **RESTful API** with Express.js
- **TypeScript** for type safety and better development experience
- **File upload support** with Multer
- **CORS enabled** for frontend integration

## Setup

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Environment configuration:**
   ```bash
   cp .env.example .env
   # Edit .env with your Roboflow API key if different
   ```

3. **Development:**
   ```bash
   npm run dev
   ```

4. **Production build:**
   ```bash
   npm run build
   npm start
   ```

## API Endpoints

### GET /health
Health check endpoint
- Returns server status and configuration info

### GET /
Root endpoint with API documentation

### POST /predict
Image prediction with optional GPS data
- **Body (multipart/form-data):**
  - `image`: Image file (required)
  - `gps_lat`: GPS latitude (optional)
  - `gps_lng`: GPS longitude (optional)
  - `gps_weight`: GPS weight percentage (default: 40)
  - `ai_weight`: AI weight percentage (default: 60)

### POST /predict-gps
GPS-only location prediction
- **Body (JSON):**
  ```json
  {
    "latitude": 12.863788,
    "longitude": 77.434897
  }
  ```

## Campus Locations

The API recognizes these campus locations:
- Main Gate
- Cross Road
- Block 1
- Students Square
- Open Auditorium
- Block 4
- Xpress Cafe
- Block 6
- Amphi theater
- PU Block
- Architecture Block

## Example Usage

### Image Prediction with GPS
```javascript
const formData = new FormData();
formData.append('image', imageFile);
formData.append('gps_lat', '12.863788');
formData.append('gps_lng', '77.434897');

const response = await fetch('http://localhost:3001/predict', {
  method: 'POST',
  body: formData
});
```

### GPS-only Prediction
```javascript
const response = await fetch('http://localhost:3001/predict-gps', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    latitude: 12.863788,
    longitude: 77.434897
  })
});
```

## Response Format

```json
{
  "predicted_class": "Main Gate",
  "confidence": 0.85,
  "probabilities": {
    "Main Gate": 0.85,
    "Block 1": 0.10,
    "Cross Road": 0.05
  },
  "method": "hybrid",
  "hybrid_prediction": {
    "final_location": "Main Gate",
    "final_confidence": 0.85,
    "gps_contribution": 40,
    "ai_contribution": 60,
    "method": "hybrid",
    "location_scores": {...}
  },
  "gps_data": {...},
  "ai_data": {...}
}
```
