import { CampusLocation } from './types';

export const ROBOFLOW_API_KEY = process.env.ROBOFLOW_API_KEY || "MBFBifupwZPFdYcEHX1u";
export const MODEL_ID = "c-tracker-awsa5/1";
export const ROBOFLOW_URL = `https://serverless.roboflow.com/${MODEL_ID}`;

// Define class labels (must match Roboflow training classes)
export const classLabels = [
  'Main Gate', 'PU Block', 'Architecture Block',
  'Cross Road', 'Block 1', 'Students Square',
  'Open Auditorium', 'Block 4', 'Xpress Cafe',
  'Block 6', 'Amphi theater'
];

// Campus location coordinates
export const campusLocations: CampusLocation[] = [
  { name: "Main Gate", lat: 12.863788, lng: 77.434897 },
  { name: "Cross Road", lat: 12.862790, lng: 77.437411 },
  { name: "Block 1", lat: 12.863154, lng: 77.437718 },
  { name: "Students Square", lat: 12.862314, lng: 77.438240 },
  { name: "Open Auditorium", lat: 12.862787, lng: 77.438580 },
  { name: "Block 4", lat: 12.862211, lng: 77.438860 },
  { name: "Xpress Cafe", lat: 12.862045, lng: 77.439374 },
  { name: "Block 6", lat: 12.862103, lng: 77.439809 },
  { name: "Amphi theater", lat: 12.861424, lng: 77.438057 },
  { name: "PU Block", lat: 12.860511, lng: 77.437249 },
  { name: "Architecture Block", lat: 12.860132, lng: 77.438592 }
];
