{"name": "c-track", "version": "0.1.0", "private": true, "scripts": {"start": "next start", "dev": "concurrently \"npm run dev:next\" \"npm run dev:flask\" \"npm run dev:ngrok\"", "next": "npx next dev --experimental-https -H 0.0.0.0 -p 3000", "flask": "python server/index.py", "ngrok": "ngrok http --url=purely-absolute-stinkbug.ngrok-free.app 5000", "build": "next build", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tensorflow/tfjs": "^4.17.0", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "axios": "^1.8.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^9.1.2", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "gsap": "^3.12.7", "input-otp": "^1.2.4", "leaflet": "^1.9.4", "leaflet-routing-machine": "^3.2.12", "lucide-react": "^0.446.0", "next": "13.5.1", "next-themes": "^0.3.0", "openrouteservice-js": "^0.3.2", "postcss": "8.4.30", "python": "^0.0.4", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.53.0", "react-leaflet": "^4.2.1", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"@types/leaflet": "^1.9.8", "@types/leaflet-routing-machine": "^3.2.8", "@types/node": "22.13.9", "typescript": "5.8.2"}}