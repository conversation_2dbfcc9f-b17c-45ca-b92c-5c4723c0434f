import { AIPrediction, GPSMatch, HybridPrediction } from '../types';

/**
 * Combine AI and GPS predictions with weighted scoring
 */
export function combinePredictions(
  aiPrediction: AIPrediction | null,
  gpsMatch: GPSMatch | null,
  gpsWeight: number = 40,
  aiWeight: number = 60
): HybridPrediction | null {
  if (!aiPrediction && !gpsMatch) {
    return null;
  }

  const totalWeight = gpsWeight + aiWeight || 1;
  const gpsW = gpsWeight / totalWeight;
  const aiW = aiWeight / totalWeight;

  const scores: Record<string, number> = {};

  // GPS contribution
  if (gpsMatch && gpsMatch.name) {
    scores[gpsMatch.name] = gpsW * gpsMatch.confidence;
  }

  // AI contribution
  if (aiPrediction && aiPrediction.probabilities) {
    for (const [loc, prob] of Object.entries(aiPrediction.probabilities)) {
      scores[loc] = (scores[loc] || 0) + aiW * prob;
    }
  }

  if (Object.keys(scores).length === 0) {
    return null;
  }

  const bestLoc = Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
  const bestScore = scores[bestLoc];

  const gpsContribution = gpsMatch && gpsMatch.name === bestLoc 
    ? (gpsW * gpsMatch.confidence) / bestScore * 100 
    : 0;

  const aiContribution = aiPrediction 
    ? (aiW * (aiPrediction.probabilities[bestLoc] || 0)) / bestScore * 100 
    : 0;

  return {
    final_location: bestLoc,
    final_confidence: bestScore,
    gps_contribution: gpsContribution,
    ai_contribution: aiContribution,
    method: gpsMatch && aiPrediction ? 'hybrid' : (gpsMatch ? 'gps-only' : 'ai-only'),
    location_scores: scores
  };
}
