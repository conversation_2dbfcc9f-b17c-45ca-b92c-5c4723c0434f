import { NextRequest, NextResponse } from 'next/server';

// Types
interface CampusLocation {
  name: string;
  lat: number;
  lng: number;
}

interface GPSMatch {
  name: string | null;
  distance: number;
  confidence: number;
  coordinates: CampusLocation | null;
}

// Campus location coordinates
const campusLocations: CampusLocation[] = [
  { name: "Main Gate", lat: 12.863788, lng: 77.434897 },
  { name: "Cross Road", lat: 12.862790, lng: 77.437411 },
  { name: "Block 1", lat: 12.863154, lng: 77.437718 },
  { name: "Students Square", lat: 12.862314, lng: 77.438240 },
  { name: "Open Auditorium", lat: 12.862787, lng: 77.438580 },
  { name: "Block 4", lat: 12.862211, lng: 77.438860 },
  { name: "Xpress Cafe", lat: 12.862045, lng: 77.439374 },
  { name: "Block 6", lat: 12.862103, lng: 77.439809 },
  { name: "Amphi theater", lat: 12.861424, lng: 77.438057 },
  { name: "PU Block", lat: 12.860511, lng: 77.437249 },
  { name: "Architecture Block", lat: 12.860132, lng: 77.438592 }
];

// Utility functions
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371e3; // Earth's radius in meters
  const phi1 = (lat1 * Math.PI) / 180;
  const phi2 = (lat2 * Math.PI) / 180;
  const dphi = ((lat2 - lat1) * Math.PI) / 180;
  const dlambda = ((lon2 - lon1) * Math.PI) / 180;

  const a = Math.sin(dphi / 2) ** 2 +
    Math.cos(phi1) * Math.cos(phi2) * Math.sin(dlambda / 2) ** 2;
  
  return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
}

function findNearestLocation(userLat: number, userLng: number): GPSMatch {
  let nearestLocation: CampusLocation | null = null;
  let minDistance = Infinity;

  for (const loc of campusLocations) {
    const dist = calculateDistance(userLat, userLng, loc.lat, loc.lng);
    if (dist < minDistance) {
      minDistance = dist;
      nearestLocation = loc;
    }
  }

  const maxDistance = 200; // meters
  const confidence = Math.max(0, Math.min(1, 1 - (minDistance / maxDistance)));

  return {
    name: nearestLocation?.name || null,
    distance: minDistance,
    confidence,
    coordinates: nearestLocation || null
  };
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { latitude, longitude } = body;

    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      return NextResponse.json(
        { error: 'Valid GPS coordinates (latitude, longitude) required' },
        { status: 400 }
      );
    }

    const gpsMatch = findNearestLocation(latitude, longitude);

    if (!gpsMatch || !gpsMatch.name) {
      return NextResponse.json(
        { error: 'No nearby campus location found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      predicted_class: gpsMatch.name,
      confidence: gpsMatch.confidence,
      distance: gpsMatch.distance,
      coordinates: gpsMatch.coordinates,
      method: 'gps-only',
      gps_data: gpsMatch
    });

  } catch (error) {
    console.error('GPS prediction error:', error);
    return NextResponse.json({
      error: 'GPS prediction failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
