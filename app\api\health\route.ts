import { NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    message: 'C-Track Locator Next.js API is running!',
    timestamp: new Date().toISOString(),
    roboflow_configured: !!process.env.ROBOFLOW_API_KEY,
    endpoints: [
      'GET /api/health - Server status',
      'POST /api/predict - Image prediction with optional GPS',
      'POST /api/predict-gps - GPS-only prediction'
    ]
  });
}
