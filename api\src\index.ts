import express from 'express';
import cors from 'cors';
import multer from 'multer';
import dotenv from 'dotenv';
import { predictWithRoboflow, bufferToBase64 } from './services/roboflow';
import { findNearestLocation } from './utils/gps';
import { combinePredictions } from './utils/hybrid';
import { PredictionResponse } from './types';

// Load environment variables
dotenv.config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    message: 'C-Track Locator TypeScript API is running!',
    timestamp: new Date().toISOString(),
    roboflow_configured: !!process.env.ROBOFLOW_API_KEY
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    message: 'C-Track Locator TypeScript API is running!',
    endpoints: [
      'GET /health - Server status',
      'POST /predict - Image prediction with optional GPS',
      'POST /predict-gps - GPS-only prediction'
    ],
    version: '1.0.0'
  });
});

// Image prediction endpoint with optional GPS
app.post('/predict', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    // Convert image to base64
    const imageBase64 = bufferToBase64(req.file.buffer);

    // Get AI prediction from Roboflow
    const aiPrediction = await predictWithRoboflow(imageBase64);

    // Parse GPS data from form
    const gpsLat = req.body.gps_lat ? parseFloat(req.body.gps_lat) : null;
    const gpsLng = req.body.gps_lng ? parseFloat(req.body.gps_lng) : null;
    const gpsWeight = req.body.gps_weight ? parseInt(req.body.gps_weight) : 40;
    const aiWeight = req.body.ai_weight ? parseInt(req.body.ai_weight) : 60;

    let response: PredictionResponse;

    // If GPS coordinates provided, do hybrid prediction
    if (gpsLat !== null && gpsLng !== null) {
      const gpsMatch = findNearestLocation(gpsLat, gpsLng);
      
      if (gpsMatch && gpsMatch.name) {
        const hybrid = combinePredictions(aiPrediction, gpsMatch, gpsWeight, aiWeight);
        
        if (hybrid) {
          response = {
            predicted_class: hybrid.final_location,
            confidence: hybrid.final_confidence,
            probabilities: aiPrediction.probabilities,
            hybrid_prediction: hybrid,
            gps_data: gpsMatch,
            ai_data: aiPrediction,
            method: hybrid.method
          };
        } else {
          // Fallback to AI-only if hybrid fails
          response = {
            predicted_class: aiPrediction.predicted_class,
            confidence: aiPrediction.probabilities[aiPrediction.predicted_class],
            probabilities: aiPrediction.probabilities,
            ai_data: aiPrediction,
            method: 'ai-only'
          };
        }
      } else {
        // GPS provided but no nearby location found, use AI-only
        response = {
          predicted_class: aiPrediction.predicted_class,
          confidence: aiPrediction.probabilities[aiPrediction.predicted_class],
          probabilities: aiPrediction.probabilities,
          ai_data: aiPrediction,
          method: 'ai-only'
        };
      }
    } else {
      // No GPS provided, AI-only prediction
      response = {
        predicted_class: aiPrediction.predicted_class,
        confidence: aiPrediction.probabilities[aiPrediction.predicted_class],
        probabilities: aiPrediction.probabilities,
        ai_data: aiPrediction,
        method: 'ai-only'
      };
    }

    res.json(response);

  } catch (error) {
    console.error('Prediction error:', error);
    res.status(500).json({
      error: 'Prediction failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// GPS-only prediction endpoint
app.post('/predict-gps', (req, res) => {
  try {
    const { latitude, longitude } = req.body;

    if (typeof latitude !== 'number' || typeof longitude !== 'number') {
      return res.status(400).json({ error: 'Valid GPS coordinates (latitude, longitude) required' });
    }

    const gpsMatch = findNearestLocation(latitude, longitude);

    if (!gpsMatch || !gpsMatch.name) {
      return res.status(404).json({ error: 'No nearby campus location found' });
    }

    res.json({
      predicted_class: gpsMatch.name,
      confidence: gpsMatch.confidence,
      distance: gpsMatch.distance,
      coordinates: gpsMatch.coordinates,
      method: 'gps-only',
      gps_data: gpsMatch
    });

  } catch (error) {
    console.error('GPS prediction error:', error);
    res.status(500).json({
      error: 'GPS prediction failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
app.listen(port, () => {
  console.log(`🚀 C-Track Locator TypeScript API server running on port ${port}`);
  console.log(`📍 Health check: http://localhost:${port}/health`);
  console.log(`🔗 API docs: http://localhost:${port}/`);
});

export default app;
