import axios from 'axios';
import { ROBOFLOW_API_KEY, ROBOFLOW_URL } from '../config';
import { RoboflowResponse, AIPrediction } from '../types';

/**
 * Make prediction using Roboflow API with base64 image
 */
export async function predictWithRoboflow(imageBase64: string): Promise<AIPrediction> {
  try {
    const response = await axios({
      method: 'POST',
      url: ROBOFLOW_URL,
      params: {
        api_key: ROBOFLOW_API_KEY
      },
      data: imageBase64,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    const roboflowData: RoboflowResponse = response.data;

    if (!roboflowData.predictions || roboflowData.predictions.length === 0) {
      throw new Error('No predictions returned from Roboflow');
    }

    // Convert Roboflow predictions to our format
    const probabilities: Record<string, number> = {};
    
    for (const pred of roboflowData.predictions) {
      probabilities[pred.class] = pred.confidence;
    }

    // Find the prediction with highest confidence
    const topPrediction = roboflowData.predictions.reduce((prev, current) => 
      prev.confidence > current.confidence ? prev : current
    );

    return {
      predicted_class: topPrediction.class,
      probabilities
    };

  } catch (error) {
    console.error('Roboflow prediction error:', error);
    throw new Error(`Roboflow API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert uploaded file buffer to base64 string
 */
export function bufferToBase64(buffer: Buffer): string {
  return buffer.toString('base64');
}
