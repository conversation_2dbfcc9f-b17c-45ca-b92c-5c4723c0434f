services:
  - type: web
    name: ctrack-locator-server-minimal
    runtime: python
    buildCommand: |
      echo "=== Python Version Check ==="
      python3 --version || python --version
      echo "=== Installing minimal dependencies first ==="
      python3 -m pip install --upgrade pip setuptools wheel
      python3 -m pip install --no-cache-dir --prefer-binary -r requirements-minimal.txt
      echo "=== Basic server deployed successfully ==="
    startCommand: gunicorn --bind 0.0.0.0:$PORT --workers 1 --timeout 120 --access-logfile - --error-logfile - index:app
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.9
      - key: PIP_NO_CACHE_DIR
        value: '1'
      - key: PYTHONUNBUFFERED
        value: '1'
      - key: PYTHONDONTWRITEBYTECODE
        value: '1'
      - key: ROBOFLOW_API_KEY
        value: MBFBifupwZPFdYcEHX1u
